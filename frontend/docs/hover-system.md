# Mobile-First Hover System

A comprehensive hover system built on top of the existing token-based design system, following mobile-first principles with performance-optimized transform animations.

## Overview

This hover system provides:
- **Mobile-first approach**: Touch states work on all devices
- **Progressive enhancement**: Hover effects only on hover-capable devices
- **Token-based consistency**: Uses CSS custom properties for automatic light/dark mode
- **Performance optimized**: Transform-based animations only
- **Space Grotesk integration**: Works with existing typography system
- **8px spacing scale**: Integrates with existing --space-* tokens

## Design Tokens

### Transform Tokens
```css
--hover-scale-sm: scale(1.02);
--hover-scale-md: scale(1.05);
--hover-scale-lg: scale(1.08);
--hover-translate-y-sm: translateY(-2px);
--hover-translate-y-md: translateY(-4px);
--hover-translate-y-lg: translateY(-8px);
```

### Timing Tokens
```css
--hover-duration-fast: 150ms;
--hover-duration-normal: 300ms;
--hover-duration-slow: 500ms;
--hover-ease: cubic-bezier(0.4, 0, 0.2, 1);
--hover-ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
```

### Color Tokens (Auto Light/Dark)
```css
/* Light Mode */
--hover-bg-primary: var(--token-interactive-primary-hover);
--hover-text-primary: var(--token-text-primary);
--hover-border-primary: var(--token-primary-300);

/* Dark Mode (automatically applied) */
--hover-bg-primary: var(--token-interactive-primary-hover);
--hover-text-primary: #ffffff;
--hover-border-primary: var(--token-primary-400);
```

## Utility Classes

### Base Interactive Classes

#### `.interactive-primary`
Primary interactive elements with full hover/touch states:
```tsx
<button className="interactive-primary interactive-base rounded-lg px-4 py-3">
  Primary Button
</button>
```

#### `.interactive-secondary`
Secondary interactive elements with subtle hover states:
```tsx
<button className="interactive-secondary interactive-base rounded-lg px-4 py-3">
  Secondary Button
</button>
```

### Transform Effects

#### `.hover-lift`
Subtle elevation effect:
```tsx
<div className="hover-lift bg-token-secondary rounded-lg p-4">
  Card with lift effect
</div>
```

#### `.hover-scale`
Size scaling effect:
```tsx
<div className="hover-scale bg-token-secondary rounded-lg p-4">
  Card with scale effect
</div>
```

#### `.hover-glow`
Glow shadow effect:
```tsx
<div className="hover-glow bg-token-secondary rounded-lg p-4">
  Card with glow effect
</div>
```

### Color Effects

#### `.hover-text-accent`
Text color change on hover:
```tsx
<span className="hover-text-accent cursor-pointer">
  Text changes color
</span>
```

#### `.hover-bg-primary`
Background color change on hover:
```tsx
<div className="hover-bg-primary rounded-lg p-4">
  Background changes
</div>
```

### Spacing-Aware Effects

#### `.hover-spacing-sm` / `.hover-spacing-md`
Padding increases using --space-* tokens:
```tsx
<div className="hover-spacing-sm bg-token-secondary rounded-lg p-2">
  Padding increases on hover
</div>
```

### Combined Effects

#### `.hover-card`
Complete card hover experience:
```tsx
<div className="hover-card bg-token-secondary rounded-lg p-6">
  <h3>Interactive Card</h3>
  <p>Combines lift, scale, shadow, and background changes</p>
</div>
```

#### `.hover-button`
Complete button hover experience:
```tsx
<button className="hover-button bg-token-interactive-primary text-white rounded-lg px-6 py-3">
  Interactive Button
</button>
```

## Mobile-First Implementation

### Touch States (Always Active)
```css
.interactive-primary {
  /* Touch states work on all devices */
  &:active {
    transform: var(--hover-scale-sm);
    background-color: var(--token-interactive-primary-active);
  }
}
```

### Hover Enhancement (Hover-Capable Devices Only)
```css
.interactive-primary {
  /* Hover states only on devices that support hover */
  @media (hover: hover) {
    &:hover {
      transform: var(--hover-scale-sm);
      background-color: var(--hover-bg-primary);
      box-shadow: var(--hover-shadow-md);
    }
  }
}
```

## Performance Considerations

1. **Transform-only animations**: All animations use `transform` and `opacity` for GPU acceleration
2. **CSS custom properties**: Automatic light/dark mode without JavaScript
3. **Media queries**: Hover effects only load on hover-capable devices
4. **Reduced motion**: Respects `prefers-reduced-motion` user preference

## Integration with Existing System

### Works with Current Tokens
- `--token-text-primary` / `--token-bg-primary` (auto dark/light)
- `--space-*` tokens for spacing calculations
- `--token-interactive-*` tokens for state management

### Space Grotesk Typography
All hover effects work seamlessly with the existing Space Grotesk font system.

### 8px Spacing Scale
Hover spacing effects use the existing `--space-*` token system:
- `--space-1` (8px) through `--space-24` (192px)
- Component spacing: `--space-component` (24px)
- Section spacing: `--space-section` (64px)

## Usage Examples

See `HoverSystemExamples.tsx` for comprehensive examples of all hover utilities in action.

## Browser Support

- **Modern browsers**: Full hover system support
- **Touch devices**: Touch states work universally
- **Legacy browsers**: Graceful degradation to basic states
