# <PERSON>folio – Technical Documentation

_Last revision : 2025-06-04_

This document captures **everything you need to run, build, and ship** the single-page portfolio hosted at **nathandryer.com**.  
There is **no backend** – the site is compiled to static files and served through **GitHub Pages**.

---

## 1 Technology Stack

| Layer         | Technology           | Version   | Purpose                                               |
| ------------- | -------------------- | --------- | ----------------------------------------------------- |
| UI Framework  | React 19             | ^19.0     | Declarative component model                           |
| Language      | TypeScript           | ^5.8      | Type-safe authoring                                   |
| Styling       | Tailwind CSS         | ^3.4      | Utility-first, dark-mode class                        |
| Animation     | Framer Motion        | ^12.12    | Declarative motion, respects `prefers-reduced-motion` |
| Tooling       | Create React App     | 5.0.1     | Zero-config React build system                        |
| Icons         | Heroicons + Lucide   | ^2.2/^0.5 | Consistent iconography                                |
| Lint / Format | ESLint v9 + Prettier | 9.23      | Consistent style & quality                            |
| Testing       | Playwright + Jest    | —         | E2E and unit testing                                  |
| Hosting       | GitHub Pages         | —         | Free CDN for static assets                            |
| CI (optional) | GitHub Actions       | —         | Lint → Build → Deploy                                 |

---

## 2 Directory Layout (simplified)

```
frontend/
├─ src/
│  ├─ components/     # Header.tsx, Timeline.tsx, etc.
│  ├─ styles/         # global.css (Tailwind imports + CSS vars)
│  ├─ types/          # shared TS interfaces
│  ├─ hooks/          # useViewTransitions.ts, etc.
│  ├─ utils/          # helper functions
│  ├─ constants/      # app constants
│  ├─ layouts/        # layout components
│  ├─ assets/         # static assets
│  ├─ data.mock.ts    # static JSON data
│  ├─ App.tsx         # SPA root
│  └─ index.tsx       # ReactDOM.createRoot(...)
├─ tests/
│  ├─ utils/          # playwright_executor.py, playwright_test.py
│  └─ *.spec.ts       # Playwright E2E tests
├─ public/            # static assets (favicon, manifest, etc.)
└─ build/             # production build output
```

> Rule : keep components flat or use small sub-folders – no complex atomic hierarchy required.

---

## 3 Local Development

1. Install Node ≥ 20

   ```bash
   git clone https://github.com/nathan-dryer/app.git
   cd app/frontend
   yarn
   ```

2. Start dev server (hot reload @ http://localhost:3000)

   ```bash
   # Method 1: Most reliable (recommended)
   cd frontend && yarn start

   # Method 2: Use simplified script
   scripts/start-dev.sh

   # Method 3: Use unified script
   scripts/dev.sh frontend

   # Check server status
   scripts/dev.sh status

   # Stop all servers
   scripts/dev.sh stop
   ```

   **Development Server Information:**

   - **Port**: 3000 (Create React App default)
   - **URL**: http://localhost:3000
   - **Package Manager**: yarn (required)
   - **Key Requirement**: Must run from frontend directory or use provided scripts

   **Troubleshooting:**

   - If port 3000 is busy: `./dev.sh stop` then restart
   - If dependencies are outdated: `cd frontend && yarn install`
   - If scripts fail: Use Method 1 (most reliable)

3. Code quality & formatting

   ```bash
   cd frontend
   yarn lint              # ESLint + TypeScript rules
   yarn lint:fix          # Auto-fix ESLint issues
   yarn format            # Format code with Prettier
   yarn format:check      # Check formatting
   yarn tsc --noEmit      # Type checking only
   ```

4. Testing

   ```bash
   cd frontend
   yarn test              # Run Jest unit tests
   yarn test:e2e          # Run Playwright E2E tests
   yarn smoke             # Run smoke tests
   ```

   **Test Structure:**

   - **Unit Tests**: Jest + React Testing Library in `src/__tests__/`
   - **E2E Tests**: Playwright tests in `tests/*.spec.ts`
   - **Test Utilities**: Python utilities in `tests/utils/` for screenshot capture and automation

---

## 3.5 View Transitions API Integration

This project uses the View Transitions API for smooth UI transitions with React state updates. The API provides responsive UI elements while maintaining smooth transitions through proper state update wrapping.

### The Problem

When implementing the View Transitions API, UI elements can become unresponsive if state updates are not properly wrapped with `document.startViewTransition()`. This happens because the browser waits for the transition to complete before processing the next state update.

### The Solution

Use the `useViewTransitions` hook to wrap all state updates that affect the UI with the `withViewTransition` function.

### Usage Pattern

```tsx
import { useViewTransitions } from "../hooks/useViewTransitions";

export const MyComponent: React.FC = () => {
  const { withViewTransition } = useViewTransitions();

  const handleStateChange = (): void => {
    withViewTransition(() => {
      setState(newValue);
    });
  };
};
```

### Implementation Examples

#### ThemeToggle Component with Optimized CSS Transitions

**Current Implementation (2025) - FIXED:**

The theme toggle now uses optimized CSS transitions instead of View Transitions API to eliminate white screen flash while maintaining smooth theme switching:

```tsx
// useTheme.ts - Proper View Transitions integration
export const useTheme = (): UseThemeReturn => {
  const [darkMode, setDarkMode] = useState<boolean>(() => {
    // Initialize from localStorage or system preference
    if (typeof window === "undefined") return false;
    const stored = localStorage.getItem("theme");
    if (stored) return stored === "dark";
    return window.matchMedia("(prefers-color-scheme: dark)").matches;
  });

  const toggleTheme = (): void => {
    const newDarkMode = !darkMode;

    // Progressive enhancement with View Transitions
    if (!("startViewTransition" in document)) {
      flushSync(() => setDarkMode(newDarkMode));
      return;
    }

    try {
      document.startViewTransition(() => {
        flushSync(() => setDarkMode(newDarkMode));
      });
    } catch (error) {
      // Fallback if View Transitions fail
      flushSync(() => setDarkMode(newDarkMode));
    }
  };

  return { darkMode, toggleTheme };
};
```

**ThemeToggle Component:**

```tsx
// ThemeToggle.tsx - Clean component using the hook
export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  darkMode,
  toggleTheme,
}) => {
  return (
    <motion.button
      onClick={toggleTheme}
      aria-label={darkMode ? "Switch to light mode" : "Switch to dark mode"}
      // ... styling and animations
    >
      {/* Icon animations with Framer Motion */}
    </motion.button>
  );
};
```

#### CommandMenu Component

**Before (Unresponsive):**

```tsx
const handleCopyEmail = (): void => {
  setCopied(true);
  setTimeout(() => {
    setCopied(false);
    setIsOpen(false);
  }, 1500);
};
```

**After (Responsive):**

```tsx
import { useViewTransitions } from "../hooks/useViewTransitions";

export const CommandMenu: React.FC<CommandMenuProps> = ({
  isOpen,
  setIsOpen,
}) => {
  const { withViewTransition } = useViewTransitions();

  const handleCopyEmail = (): void => {
    withViewTransition(() => {
      setCopied(true);
    });
    setTimeout(() => {
      withViewTransition(() => {
        setCopied(false);
        setIsOpen(false);
      });
    }, 1500);
  };
  // ... rest of component
};
```

### View Transitions API CSS Animations

**Custom CSS for Theme Transitions:**

```css
/* View Transitions API Styles */
@media (prefers-reduced-motion: no-preference) {
  /* Root view transition for theme changes */
  ::view-transition-old(root),
  ::view-transition-new(root) {
    animation-duration: 0.5s;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Theme transition: fade with slight scale */
  ::view-transition-old(root) {
    animation-name: theme-fade-out;
  }

  ::view-transition-new(root) {
    animation-name: theme-fade-in;
  }

  @keyframes theme-fade-out {
    from {
      opacity: 1;
      transform: scale(1);
    }
    to {
      opacity: 0;
      transform: scale(1.02);
    }
  }

  @keyframes theme-fade-in {
    from {
      opacity: 0;
      transform: scale(0.98);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}

/* Respect reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  ::view-transition-old(root),
  ::view-transition-new(root) {
    animation-duration: 0.1s;
  }
}
```

### Best Practices

#### When to Use View Transitions

✅ **DO wrap these state updates:**

- Theme changes (dark/light mode) ← **Primary use case**
- Tab switching
- Filter changes
- Accordion expand/collapse
- Page navigation
- Content section changes
- Any visual state change that affects layout (but NOT modals)

❌ **DON'T wrap these state updates:**

- Form input changes (typing)
- Hover states
- Focus states
- Temporary loading states
- High-frequency updates (animations, timers)
- **Modal/dialog open/close** ← **Critical: Can cause state conflicts**
- Tooltip show/hide
- Menu open/close

#### Pattern for Event Handlers

```tsx
// ✅ Correct pattern
const handleClick = (): void => {
  withViewTransition(() => {
    setState(newValue);
  });
};

// ❌ Incorrect pattern
const handleClick = (): void => {
  setState(newValue); // Not wrapped
};
```

#### Pattern for Multiple State Updates

```tsx
// ✅ Correct - wrap all related state updates together
const handleComplexAction = (): void => {
  withViewTransition(() => {
    setStateA(valueA);
    setStateB(valueB);
    setStateC(valueC);
  });
};

// ❌ Incorrect - separate wrapping can cause issues
const handleComplexAction = (): void => {
  withViewTransition(() => setStateA(valueA));
  withViewTransition(() => setStateB(valueB));
  withViewTransition(() => setStateC(valueC));
};
```

#### Pattern for Async Operations

```tsx
// ✅ Correct pattern for async state updates
const handleAsyncAction = async (): Promise<void> => {
  const result = await fetchData();

  withViewTransition(() => {
    setData(result);
    setLoading(false);
  });
};
```

### Error Handling & Progressive Enhancement

The implementation includes comprehensive error handling and progressive enhancement:

```tsx
// Feature detection with proper TypeScript support
const isSupported = useMemo((): boolean => {
  return (
    typeof document !== "undefined" &&
    "startViewTransition" in document &&
    typeof document.startViewTransition === "function"
  );
}, []);

// Robust implementation with multiple fallback layers
const withViewTransition = useCallback(
  (
    callback: () => void,
    options?: { skipOnReducedMotion?: boolean }
  ): ViewTransition | void => {
    // Respect reduced motion preference
    if (
      options?.skipOnReducedMotion &&
      window.matchMedia("(prefers-reduced-motion: reduce)").matches
    ) {
      callback();
      return;
    }

    if (isSupported) {
      try {
        const doc = document as DocumentWithViewTransitions;
        return doc.startViewTransition(callback);
      } catch (error) {
        console.warn("View Transitions failed, falling back:", error);
        callback(); // Graceful fallback
      }
    } else {
      callback(); // Fallback for unsupported browsers
    }
  },
  [isSupported]
);
```

**Key Features:**

- **Progressive Enhancement**: Works in all browsers, enhanced in supported ones
- **Accessibility**: Respects `prefers-reduced-motion` setting
- **Type Safety**: Custom TypeScript declarations to avoid conflicts with existing types
- **Error Recovery**: Graceful fallback if transitions fail
- **Performance**: No overhead in unsupported browsers
- **Selective Usage**: Only applied to appropriate state changes (not modals/tooltips)

### Browser Support & Performance

- **Chrome 111+, Edge 111+**: Full support
- **Safari/Firefox**: Automatic fallback to regular state updates
- **Progressive enhancement**: Works in all browsers with enhanced experience in supported ones
- **GPU-accelerated**: Performant transitions
- **Respects `prefers-reduced-motion`**: Accessibility compliant
- **No performance impact**: On unsupported browsers

### Testing

The implementation maintains compatibility with existing tests. The hook provides fallback behavior when View Transitions API is not available (like in test environments).

---

## 4 Build & GitHub Pages Deployment

### 4.1 Build

```bash
cd frontend
yarn build           # outputs static files to build/
```

### 4.2 One-command publish (manual)

```bash
# install once
yarn add -D gh-pages

# package.json
"scripts": {
  "predeploy": "yarn build",
  "deploy": "gh-pages -d build -b gh-pages"
}

# then
yarn deploy
```

This pushes the `build/` folder to the `gh-pages` branch, automatically served at
`https://<github-username>.github.io/<repo-name>/`.

### 4.3 Custom Domain (nathandryer.com)

1. In repo → Settings → Pages → **Custom domain** → `www.nathandryer.com`
2. Add a DNS **CNAME** record:
   ```
   www   CNAME   <github-username>.github.io
   ```
3. (Optional) Root domain redirect: use an ALIAS/ANAME to `www` or an A record to GitHub’s IPs.
4. Enable “Enforce HTTPS”.

---

## 5 Continuous Deployment (optional)

`.github/workflows/pages.yml`

```yaml
name: Pages CI
on:
  push:
    branches: [main]
jobs:
  build-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with: { node-version: 20 }
      - run: |
          cd frontend
          yarn
          yarn lint && yarn tsc --noEmit
          yarn build
      - uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: frontend/build
```

---

## 6 Accessibility & Performance Checklist

- 44 px × 44 px minimum tap targets (`min-w-[44px] min-h-[44px]`).
- Focus indication via `.focus-ring` utility.
- Alt text for all images; empty `alt=""` if decorative.
- Colour contrast ≥ 4.5 : 1 (WCAG AA).
- Use `prefers-reduced-motion` to disable non-essential animations.
- Lighthouse score ≥ 90 on Performance, Accessibility, Best Practices, SEO.

---

## 7 Troubleshooting

| Symptom                           | Fix                                                                                  |
| --------------------------------- | ------------------------------------------------------------------------------------ |
| Tailwind classes not applied      | Ensure `@tailwind base; components; utilities;` in `global.css`.                     |
| Broken relative links on GH Pages | Confirm `package.json` has correct `homepage` field or use relative paths.           |
| 404 after page refresh            | This is a true SPA; configure `<HashRouter>` or use GitHub Pages 404 redirect trick. |
| Custom domain not HTTPS           | Wait up to 24 h after enabling "Enforce HTTPS".                                      |

---

## 8 Glossary

- **SPA** – Single-Page Application (front-end routing only).
- **Token** – Named design value (spacing, colour, etc.).
- **Tailwind CLI** – PostCSS engine generating utility classes.
- **GitHub Pages** – Static hosting service backed by a `gh-pages` branch.

_End of Technical Documentation_
