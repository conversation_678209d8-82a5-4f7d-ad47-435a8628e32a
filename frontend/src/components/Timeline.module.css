/* ◀︎ LLM-modified */
/* Timeline Component Hover States and Micro-interactions */
/* Integrates with design token system and respects prefers-reduced-motion */

/* === TIMELINE ITEM HOVER STATES === */

.timeline-item-hover {
  /* Base state - smooth transitions for all hover effects */
  transition: transform var(--motion-fast) var(--easing-standard);
}

.timeline-item-hover:hover {
  /* Hover transformation using design tokens */
  transform: scale(1.02) translateY(-2px);
}

/* Timeline card hover enhancements */
.timeline-item-hover:hover .timeline-card {
  /* Enhanced shadow and backdrop blur on hover */
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px var(--token-primary-500);
  backdrop-filter: var(--token-backdrop-blur-lg);
}

/* === TIMELINE DOT GLOW EFFECTS === */
/* ◀︎ LLM-modified: Removed all hover effects and glow animations to prevent erratic icon movement */

/* Timeline icons - stable styling without hover effects */
.timeline-item-hover .timeline-icon,
.timeline-item-hover .timeline-icon:hover,
.timeline-item-hover .timeline-icon:active,
.timeline-item-hover .timeline-icon:focus {
  /* Stable icon styling - no transforms or transitions */
  transform: none !important;
  scale: 1 !important;
  background-color: var(--token-primary-500) !important;
  transition: none !important;
}

.timeline-item-hover:hover .timeline-icon {
  /* No hover effects - keep stable appearance */
  transform: none !important;
  scale: 1 !important;
  background-color: var(--token-primary-500) !important;
}

/* Direct hover on timeline icon itself - no effects */
.timeline-icon:hover {
  transform: none !important;
  scale: 1 !important;
  background-color: var(--token-primary-500) !important;
}

/* === TIMELINE CARD FOCUS STATES === */

.timeline-card:focus-visible {
  /* Enhanced focus ring using design tokens */
  outline: 2px solid var(--token-primary-500);
  outline-offset: 4px;
  box-shadow:
    0 0 0 4px var(--token-primary-500),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* === YEAR LABEL HOVER EFFECTS === */

.timeline-item-hover:hover .timeline-year-text {
  /* Subtle color shift on hover */
  color: var(--token-primary-500);
  transform: translateY(-1px);
  transition: all var(--motion-fast) var(--easing-standard);
}

/* === RESPONSIVE HOVER STATES === */

/* Mobile: Reduce hover effects for touch devices */
@media (hover: none) and (pointer: coarse) {
  .timeline-item-hover:hover {
    transform: none;
  }

  .timeline-item-hover:hover .timeline-card {
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .timeline-item-hover .timeline-icon,
  .timeline-item-hover .timeline-icon:hover,
  .timeline-item-hover .timeline-icon:active,
  .timeline-item-hover:hover .timeline-icon,
  .timeline-icon:hover {
    /* Mobile: NO EFFECTS - stable appearance */
    transform: none !important;
    scale: 1 !important;
    background-color: var(--token-primary-500) !important;
  }
}

/* Desktop: Enhanced hover effects */
@media (hover: hover) and (pointer: fine) {
  .timeline-item-hover:hover {
    /* More pronounced hover effect on desktop */
    transform: scale(1.03) translateY(-4px);
  }
}

/* === ACCESSIBILITY: REDUCED MOTION === */

@media (prefers-reduced-motion: reduce) {

  .timeline-item-hover,
  .timeline-item-hover:hover,
  .timeline-icon,
  .timeline-icon:hover,
  .timeline-icon:active,
  .timeline-icon:focus,
  .timeline-item-hover .timeline-icon,
  .timeline-item-hover .timeline-icon:hover,
  .timeline-item-hover .timeline-icon:active,
  .timeline-item-hover:hover .timeline-icon,
  .timeline-item-hover:hover .timeline-year-text {
    /* Disable ALL animations and transforms */
    transform: none !important;
    scale: 1 !important;
    transition: none !important;
    background-color: var(--token-primary-500) !important;
  }

  .timeline-item-hover:hover .timeline-card {
    /* Keep subtle shadow but remove transforms */
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 0 0 1px var(--token-primary-500);
    transition: none !important;
  }

  .timeline-item-hover:hover .timeline-icon,
  .timeline-icon:hover {
    /* No effects for reduced motion */
    transition: none !important;
    transform: none !important;
    scale: 1 !important;
    background-color: var(--token-primary-500) !important;
  }
}

/* === TIMELINE ITEM ACTIVE STATES === */

.timeline-item-hover:active .timeline-card {
  /* Subtle press effect */
  transform: scale(0.98);
  transition: transform 0.1s var(--easing-standard);
}

@media (prefers-reduced-motion: reduce) {
  .timeline-item-hover:active .timeline-card {
    transform: none !important;
  }
}

/* === HIGH CONTRAST MODE SUPPORT === */

@media (prefers-contrast: high) {
  .timeline-item-hover:hover .timeline-card {
    border: 2px solid var(--token-primary-600);
  }

  .timeline-item-hover:hover .timeline-icon {
    border: 2px solid var(--token-primary-600) !important;
    box-shadow: none !important;
    transform: none !important;
    scale: 1 !important;
  }
}