// ◀︎ LLM-modified
import React, { useState, useEffect } from 'react';
import { Command } from 'lucide-react';

interface CommandMenuButtonProps {
  toggleCommandMenu: () => void;
  className?: string;
  children?: React.ReactNode;
}

/**
 * CommandMenuButton - Simplified without extra animations
 */
export const CommandMenuButton: React.FC<CommandMenuButtonProps> = ({
  toggleCommandMenu,
  className = '',
  children,
}) => {
  const [isMac, setIsMac] = useState<boolean>(false);

  // Detect OS for keyboard shortcut display
  useEffect(() => {
    setIsMac(navigator.platform.toUpperCase().indexOf('MAC') >= 0);
  }, []);

  // Simplified - no complex animations


  return (
    <div
      className={`command-pill flex min-h-touch-comfortable cursor-pointer items-center rounded-full border border-white/20 bg-transparent px-4 py-3 text-white shadow-token-lg backdrop-blur-md transition-all duration-token-normal focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75 sm:py-3 ${className}`}
      style={{
        background: 'var(--token-bg-frosted-strong)',
        boxShadow: 'var(--shadow-lg), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
      }}
      onClick={toggleCommandMenu}
      aria-label="Open command palette"
      aria-keyshortcuts="⌘+K"
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          toggleCommandMenu();
        }
      }}
    >
      {children || (
        <>
          <Command size={18} className="mr-3 text-white/90" />
          <span className="flex-1 text-left text-white/90">
            Open Command Menu
          </span>
          <kbd className="hidden items-center justify-center rounded bg-white/10 px-2 py-1 text-xs text-white/80 md:flex">
            {isMac ? '⌘K' : 'Ctrl+K'}
          </kbd>
        </>
      )}
    </div>
  );
};
