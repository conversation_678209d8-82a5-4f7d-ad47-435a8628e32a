// ◀︎ LLM-modified
import React, { useState, useEffect } from 'react';

/**
 * Debug component to test CommandMenu functionality
 * This component helps identify if the CommandMenu is working correctly
 */
export const CommandMenuDebug: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  useEffect(() => {
    const addDebugInfo = (message: string) => {
      setDebugInfo(prev => [...prev.slice(-4), `${new Date().toLocaleTimeString()}: ${message}`]);
    };

    // Test keyboard shortcut
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        addDebugInfo('Cmd+K / Ctrl+K detected');
      }
    };

    // Test if CommandMenu component exists
    const commandMenuExists = document.querySelector('[role="dialog"]');
    addDebugInfo(`CommandMenu dialog element: ${commandMenuExists ? 'Found' : 'Not found'}`);

    // Test if command buttons exist
    const commandButtons = document.querySelectorAll('.command-pill');
    addDebugInfo(`Command buttons found: ${commandButtons.length}`);

    // Test if design tokens are loaded
    const rootStyles = getComputedStyle(document.documentElement);
    const tokenBgPrimary = rootStyles.getPropertyValue('--bg-token-primary');
    addDebugInfo(`Design token --bg-token-primary: ${tokenBgPrimary || 'Not found'}`);

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <div 
      className="fixed top-4 right-4 z-[100] bg-black/80 text-white p-4 rounded-lg text-xs max-w-xs"
      style={{ fontFamily: 'monospace' }}
    >
      <h3 className="font-bold mb-2">CommandMenu Debug</h3>
      <div className="space-y-1">
        {debugInfo.map((info, index) => (
          <div key={index} className="text-green-400">
            {info}
          </div>
        ))}
      </div>
      <div className="mt-2 pt-2 border-t border-white/20">
        <div className="text-yellow-400">Press Cmd+K / Ctrl+K to test</div>
      </div>
    </div>
  );
};
