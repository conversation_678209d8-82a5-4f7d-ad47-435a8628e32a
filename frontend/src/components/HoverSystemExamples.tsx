// ◀︎ LLM-modified
import React from 'react';

/**
 * Hover System Examples - Demonstrates mobile-first hover utilities
 * 
 * Features:
 * - Mobile-first: Touch states work on all devices
 * - Hover enhancement: @media (hover: hover) adds hover effects
 * - Token-based: Uses CSS custom properties for consistency
 * - Performance: Transform-based animations only
 * - Accessibility: Respects user preferences
 */

export const HoverSystemExamples: React.FC = () => {
  return (
    <div className="space-y-8 p-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-token-primary">
          Mobile-First Hover System Examples
        </h2>
        <p className="text-token-secondary">
          All examples work on touch devices with enhanced hover states on desktop.
        </p>
      </div>

      {/* Interactive Base Classes */}
      <section className="space-y-4">
        <h3 className="text-xl font-semibold text-token-primary">
          Interactive Base Classes
        </h3>
        
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Primary Interactive */}
          <button className="rounded-lg px-4 py-3 font-medium interactive-base interactive-primary">
            Primary Interactive
          </button>
          
          {/* Secondary Interactive */}
          <button className="rounded-lg px-4 py-3 font-medium interactive-base interactive-secondary">
            Secondary Interactive
          </button>
        </div>
      </section>

      {/* Transform Effects */}
      <section className="space-y-4">
        <h3 className="text-xl font-semibold text-token-primary">
          Transform Effects
        </h3>
        
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          {/* Lift Effect */}
          <div className="cursor-pointer rounded-lg p-4 bg-token-secondary hover-lift">
            <h4 className="font-medium text-token-primary">Hover Lift</h4>
            <p className="text-sm text-token-tertiary">Elevates on hover</p>
          </div>
          
          {/* Scale Effect */}
          <div className="cursor-pointer rounded-lg p-4 bg-token-secondary hover-scale">
            <h4 className="font-medium text-token-primary">Hover Scale</h4>
            <p className="text-sm text-token-tertiary">Scales on hover</p>
          </div>
          
          {/* Glow Effect */}
          <div className="cursor-pointer rounded-lg p-4 bg-token-secondary hover-glow">
            <h4 className="font-medium text-token-primary">Hover Glow</h4>
            <p className="text-sm text-token-tertiary">Glows on hover</p>
          </div>
        </div>
      </section>

      {/* Color Effects */}
      <section className="space-y-4">
        <h3 className="text-xl font-semibold text-token-primary">
          Color Effects
        </h3>
        
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Text Hover */}
          <div className="rounded-lg p-4 bg-token-secondary">
            <p className="cursor-pointer hover-text-accent">
              Text changes color on hover
            </p>
          </div>
          
          {/* Background Hover */}
          <div className="cursor-pointer rounded-lg p-4 transition-colors hover-bg-primary">
            <p className="text-token-primary">
              Background changes on hover
            </p>
          </div>
        </div>
      </section>

      {/* Spacing-Aware Effects */}
      <section className="space-y-4">
        <h3 className="text-xl font-semibold text-token-primary">
          Spacing-Aware Effects
        </h3>
        
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Small Spacing */}
          <div className="cursor-pointer rounded-lg p-2 bg-token-secondary hover-spacing-sm">
            <p className="text-token-primary">Small spacing increase</p>
          </div>
          
          {/* Medium Spacing */}
          <div className="cursor-pointer rounded-lg p-3 bg-token-secondary hover-spacing-md">
            <p className="text-token-primary">Medium spacing increase</p>
          </div>
        </div>
      </section>

      {/* Combined Effects */}
      <section className="space-y-4">
        <h3 className="text-xl font-semibold text-token-primary">
          Combined Effects
        </h3>
        
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Card Hover */}
          <div className="rounded-lg p-6 bg-token-secondary hover-card">
            <h4 className="mb-2 font-medium text-token-primary">Hover Card</h4>
            <p className="text-token-tertiary">
              Combines lift, scale, shadow, and background changes
            </p>
          </div>
          
          {/* Button Hover */}
          <button className="rounded-lg bg-token-interactive-primary px-6 py-3 font-medium text-white hover-button">
            Hover Button
          </button>
        </div>
      </section>

      {/* Usage Examples */}
      <section className="space-y-4">
        <h3 className="text-xl font-semibold text-token-primary">
          Usage Examples
        </h3>
        
        <div className="space-y-4 rounded-lg p-6 bg-token-secondary">
          <h4 className="font-medium text-token-primary">Code Examples:</h4>
          
          <div className="space-y-2 font-mono text-sm">
            <div className="rounded p-2 bg-token-tertiary">
              <code>{`<button className="interactive-primary interactive-base rounded-lg px-4 py-3">`}</code>
            </div>
            <div className="rounded p-2 bg-token-tertiary">
              <code>{`<div className="hover-card bg-token-secondary rounded-lg p-6">`}</code>
            </div>
            <div className="rounded p-2 bg-token-tertiary">
              <code>{`<span className="hover-text-accent cursor-pointer">`}</code>
            </div>
          </div>
        </div>
      </section>

      {/* Design Tokens Reference */}
      <section className="space-y-4">
        <h3 className="text-xl font-semibold text-token-primary">
          Available Design Tokens
        </h3>
        
        <div className="rounded-lg p-6 bg-token-secondary">
          <div className="grid grid-cols-1 gap-6 text-sm md:grid-cols-2">
            <div>
              <h4 className="mb-2 font-medium text-token-primary">Transform Tokens:</h4>
              <ul className="space-y-1 font-mono text-token-tertiary">
                <li>--hover-scale-sm (1.02)</li>
                <li>--hover-scale-md (1.05)</li>
                <li>--hover-scale-lg (1.08)</li>
                <li>--hover-translate-y-sm (-2px)</li>
                <li>--hover-translate-y-md (-4px)</li>
                <li>--hover-translate-y-lg (-8px)</li>
              </ul>
            </div>
            
            <div>
              <h4 className="mb-2 font-medium text-token-primary">Timing Tokens:</h4>
              <ul className="space-y-1 font-mono text-token-tertiary">
                <li>--hover-duration-fast (150ms)</li>
                <li>--hover-duration-normal (300ms)</li>
                <li>--hover-duration-slow (500ms)</li>
                <li>--hover-ease (cubic-bezier)</li>
                <li>--hover-ease-bounce (bounce)</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};
