/* ◀︎ LLM-modified */
/* CommandMenu Component Styles - Fixed Framer Motion animation duration error by using proper design tokens */

/* === KEYFRAME ANIMATIONS === */

/* ◀︎ LLM-modified: Glow effect animation using brand blue gradient */
@keyframes glowRotate {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* === COMMAND PILL BASE STYLES === */

.command-pill {
  /* Basic positioning for command menu pills */
  position: relative;
  overflow: hidden;
}

/* ◀︎ LLM-modified: Glow effect background using brand blue gradient */
.command-pill::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
      var(--token-primary-400),
      var(--token-primary-500),
      var(--token-primary-600),
      var(--token-primary-700),
      var(--token-primary-600),
      var(--token-primary-500),
      var(--token-primary-400));
  background-size: 300% 300%;
  border-radius: inherit;
  z-index: -1;
  filter: blur(4px);
  opacity: 0;
  transition: opacity var(--duration-modal) var(--easing-standard);
  animation: glowRotate 3s linear infinite;
}

/* ◀︎ LLM-modified: Trigger glow effect on hover */
.command-pill:hover::before {
  opacity: 1;
}

/* ◀︎ LLM-modified: Mask layer to preserve button appearance */
.command-pill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  border-radius: inherit;
  z-index: 1;
}

/* === ACCESSIBILITY & REDUCED MOTION === */

/* ◀︎ LLM-modified: Respect prefers-reduced-motion accessibility requirements */
@media (prefers-reduced-motion: reduce) {
  .command-pill::before {
    animation: none;
    background: var(--token-primary-500);
    background-size: 100% 100%;
  }

  .command-pill:hover::before {
    opacity: 0.6;
  }
}