// ◀︎ LLM-modified
import React, { useState } from 'react';
import CommandMenu from './CommandMenu.jsx';

/**
 * CommandMenuDemo Component
 * 
 * A simple demo component to showcase the CommandMenu.jsx component.
 * This demonstrates how to integrate the command menu with design tokens.
 */
const CommandMenuDemo = () => {
  const [isOpen, setIsOpen] = useState(false);

  const handleOpenMenu = () => {
    setIsOpen(true);
  };

  const handleCloseMenu = () => {
    setIsOpen(false);
  };

  return (
    <div className="min-h-screen bg-token-bg-primary flex items-center justify-center p-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-token-text-primary mb-4">
          CommandMenu.jsx Demo
        </h1>
        <p className="text-token-text-secondary mb-8 max-w-md">
          This demo showcases the basic CommandMenu.jsx component built with cmdk 
          and integrated with our design token system.
        </p>
        
        <button
          onClick={handleOpenMenu}
          className="px-6 py-3 bg-token-primary-500 text-white rounded-lg hover:bg-token-primary-600 transition-colors duration-200 font-medium"
        >
          Open Command Menu
        </button>
        
        <div className="mt-8 text-sm text-token-text-tertiary">
          <p>Features demonstrated:</p>
          <ul className="mt-2 space-y-1">
            <li>• Design token integration (--command-menu-bg, --command-menu-border, --command-menu-shadow)</li>
            <li>• Command.Dialog with overlay</li>
            <li>• Command.Input with search functionality</li>
            <li>• Command.List with basic items</li>
            <li>• CSS Module styling</li>
            <li>• Responsive design</li>
          </ul>
        </div>
      </div>

      {/* Render CommandMenu when open */}
      {isOpen && <CommandMenu onDismiss={handleCloseMenu} />}
    </div>
  );
};

export default CommandMenuDemo;
