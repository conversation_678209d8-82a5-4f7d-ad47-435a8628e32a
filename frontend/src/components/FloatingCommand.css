/* ◀︎ LLM-modified */
/* FloatingCommand Component Styles - Idle drift and pulse ring animations */
/* Uses CSS animations for optimal performance (GPU-accelerated) */
/* ◀︎ LLM-modified: Added unified hover/focus scale behavior with no color changes */

/* === KEYFRAME ANIMATIONS === */

@keyframes idleDrift {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-2px);
  }
}

@keyframes pulseRing {
  from {
    transform: scale(1);
    opacity: 0.35;
  }

  to {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* === FLOATING COMMAND BUTTON STYLES === */

.floating-command {
  /* ◀︎ LLM-modified: Responsive positioning using design tokens to prevent overlap */
  position: fixed;
  bottom: var(--fab-desktop-bottom);
  right: var(--fab-desktop-side);

  /* Idle drift animation - subtle ±2px vertical movement over 6s with 4s delay */
  animation: idleDrift 6s var(--easing-standard) infinite 4s;

  /* Smooth transform transition for hover scale */
  transition: transform var(--motion-fast) var(--easing-standard);

  /* Ensure proper z-index and accessibility */
  z-index: 40;
}

/* ◀︎ LLM-modified: Ensure button content is visible */
.floating-command button {
  position: relative;
  z-index: 2;
}

/* ◀︎ LLM-modified: Ensure icon content is properly positioned */
.floating-command button>div {
  position: relative;
  z-index: 3;
}



/* Pulse ring effect via pseudo-element */
.floating-command::after {
  content: '';
  position: absolute;
  inset: 0;
  border: 2px solid var(--ring-accent);
  border-radius: 50%;
  opacity: 0;
  pointer-events: none;
  transform: scale(1);
}

/* Trigger pulse ring on hover and focus */
.floating-command:hover::after,
.floating-command:focus-visible::after {
  animation: pulseRing var(--motion-medium) var(--easing-standard) forwards;
}

/* Scale transform on hover and focus */
.floating-command:hover,
.floating-command:focus-visible {
  transform: scale(var(--fab-hover-scale));
}

/* === ACCESSIBILITY & REDUCED MOTION === */

@media (prefers-reduced-motion: reduce) {
  .floating-command {
    animation: none;
    /* Don't override transform: none here to allow hover scale */
  }

  .floating-command:hover::after,
  .floating-command:focus-visible::after {
    animation: none;
    opacity: 0.35;
  }
}

/* === FLOATING SCROLL BUTTON POSITIONING === */
/* ◀︎ LLM-modified: Responsive positioning for scroll button to match command button alignment */

.floating-scroll-button {
  bottom: var(--fab-desktop-bottom);
}

@media (max-width: 768px) {
  .floating-scroll-button {
    bottom: var(--fab-mobile-bottom);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .floating-scroll-button {
    bottom: var(--fab-tablet-bottom);
  }
}

/* === THEME TOGGLE RESPONSIVE POSITIONING === */
/* ◀︎ LLM-modified: Responsive positioning for theme toggle using design tokens */

@media (min-width: 769px) and (max-width: 1024px) {
  .header-theme-toggle {
    right: var(--fab-tablet-side) !important;
    top: var(--fab-tablet-side) !important;
  }
}

@media (min-width: 1025px) {
  .header-theme-toggle {
    right: var(--fab-desktop-side) !important;
    top: var(--fab-desktop-side) !important;
  }
}

/* === RESPONSIVE ADJUSTMENTS === */

/* ◀︎ LLM-modified: Responsive positioning using design tokens for consistent spacing */
@media (max-width: 768px) {
  .floating-command {
    bottom: var(--fab-mobile-bottom);
    right: var(--fab-mobile-side);
    z-index: 35;
    /* Lower z-index to prevent overlap with command menu pill */
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .floating-command {
    bottom: var(--fab-tablet-bottom);
    right: var(--fab-tablet-side);
    z-index: 40;
    /* Standard z-index for tablet/desktop */
  }
}