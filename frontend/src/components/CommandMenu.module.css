/* ◀︎ LLM-modified */
/* CommandMenu.jsx Component Styles - Using Design Tokens */

/* === COMMAND MENU DIALOG STYLES === */

.command-menu-dialog {
  /* Base dialog styling is handled via inline styles using design tokens */
  position: relative;
  z-index: 50;
}

/* === COMMAND MENU ROOT STYLES === */

.command-menu-root {
  /* Base root styling is handled via inline styles */
}

/* === COMMAND MENU INPUT STYLES === */

.command-menu-input {
  /* Base input styling is handled via inline styles */
  color: var(--token-text-primary);
}

.command-menu-input::placeholder {
  color: var(--token-text-tertiary);
}

.command-menu-input:focus {
  outline: none;
  border-bottom-color: var(--token-primary-500);
}

/* === COMMAND MENU LIST STYLES === */

.command-menu-list {
  /* Base list styling is handled via inline styles */
}

/* Custom scrollbar for command menu list */
.command-menu-list::-webkit-scrollbar {
  width: var(--scrollbar-width);
}

.command-menu-list::-webkit-scrollbar-track {
  background: var(--scrollbar-track-color);
  border-radius: var(--scrollbar-track-radius);
}

.command-menu-list::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color);
  border-radius: var(--scrollbar-thumb-radius);
}

.command-menu-list::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-color);
}

/* === COMMAND MENU ITEM STYLES === */

.command-menu-item {
  /* Base item styling is handled via inline styles */
  color: var(--token-text-primary);
  /* Enhanced text rendering for crisp fonts on glassmorphism backgrounds */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  /* Improved letter spacing for better character definition */
  letter-spacing: 0.01em;
}

/* Hover state using design tokens */
.command-menu-item:hover {
  background-color: var(--command-menu-item-hover);
  color: var(--token-text-primary);
}

/* Selected/focused state */
.command-menu-item[data-selected="true"] {
  background-color: var(--token-primary-100);
  color: var(--token-primary-800);
}

/* Focus visible state for accessibility */
.command-menu-item:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
}

/* === RESPONSIVE DESIGN === */

/* Mobile optimizations */
@media (max-width: 768px) {
  .command-menu-dialog {
    margin: 1rem;
    max-width: var(--token-command-width-mobile);
    max-height: calc(100vh - 2rem);
    width: var(--token-command-width-mobile);
  }

  .command-menu-item {
    padding: var(--token-command-item-padding-responsive-y) var(--token-command-item-padding-responsive-x) !important;
    min-height: var(--touch-target-min);
    font-size: var(--token-typography-size-responsive-sm) !important;
  }

  .command-menu-container {
    padding: var(--token-command-padding-responsive-y) var(--token-command-padding-responsive-x) !important;
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .command-menu-dialog {
    max-width: var(--token-command-width-tablet);
    width: var(--token-command-width-tablet);
  }

  .command-menu-item {
    font-size: var(--token-typography-size-responsive-sm) !important;
  }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
  .command-menu-dialog {
    max-width: var(--token-command-width-desktop);
    width: var(--token-command-width-desktop);
  }
}

/* === ACCESSIBILITY === */

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .command-menu-item {
    transition: none !important;
  }

  .command-menu-input {
    transition: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .command-menu-dialog {
    border-width: 2px;
  }

  .command-menu-item:hover {
    border: 1px solid var(--token-primary-500);
  }
}