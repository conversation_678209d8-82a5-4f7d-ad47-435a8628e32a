/* ◀︎ LLM-modified */
/* Motion Design Tokens - Centralized animation timing and easing values */
/* Ensures consistent motion language across the application */

:root {
  /* === MOTION TIMING TOKENS === */

  /* Duration tokens for different animation speeds */
  --motion-instant: 0ms;
  --motion-fast: 180ms;
  --motion-medium: 250ms;
  --motion-slow: 6000ms;

  /* === EASING TOKENS === */

  /* Standard easing curves for consistent motion feel */
  --easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-decelerate: cubic-bezier(0.0, 0, 0.2, 1);
  --easing-accelerate: cubic-bezier(0.4, 0, 1, 1);
  --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* === RING ACCENT TOKEN === */

  /* Ring accent color for pulse effects - uses darker blue for light mode contrast */
  --ring-accent: var(--token-primary-700);
  /* darker blue - #1d4ed8 for better contrast on light backgrounds */

  /* === FLOATING ACTION BUTTON TOKENS === */

  /* Semantic scale token for FAB hover state */
  --fab-hover-scale: 1.05;
}

/* === DARK MODE OVERRIDES === */
.dark {
  /* Enhanced ring accent for dark mode visibility */
  --ring-accent: var(--token-primary-400);
  /* lighter blue - #60a5fa for better contrast against dark backgrounds */
}