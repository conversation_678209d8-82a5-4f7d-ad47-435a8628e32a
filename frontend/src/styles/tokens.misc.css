/* ◀︎ LLM-modified */
/* Miscellaneous Design Tokens - Glass effects and interaction states */
/* Provides semantic tokens for frosted glass micro-interactions */

:root {
  /* === GLASS EFFECT TOKENS === */

  /* Glass highlight for sweep animations - semi-transparent white overlay */
  --glass-highlight: rgba(255, 255, 255, 0.35);

  /* Glass press effect - subtle brightness reduction for tactile feedback */
  --glass-press: brightness(0.95);
}