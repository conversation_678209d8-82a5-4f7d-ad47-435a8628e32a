/* ◀︎ LLM-modified */
/* Design Token System - Centralized 8-token color palette with light/dark mode support */
/* Maintains WCAG AA contrast compliance (≥4.5:1) for all body text */

:root {
  /* === 8-TOKEN COLOR PALETTE === */

  /* Primary - Electric Blue (Brand Color) */
  --token-primary-50: #eff6ff;
  --token-primary-100: #dbeafe;
  --token-primary-200: #bfdbfe;
  --token-primary-300: #93c5fd;
  --token-primary-400: #60a5fa;
  --token-primary-500: #3b82f6;
  /* Main brand color */
  --token-primary-600: #2563eb;
  --token-primary-700: #1d4ed8;
  --token-primary-800: #1e40af;
  --token-primary-900: #1e3a8a;
  --token-primary-950: #172554;

  /* Secondary - Deep Navy */
  --token-secondary-50: #f8fafc;
  --token-secondary-100: #f1f5f9;
  --token-secondary-200: #e2e8f0;
  --token-secondary-300: #cbd5e1;
  --token-secondary-400: #94a3b8;
  --token-secondary-500: #64748b;
  --token-secondary-600: #475569;
  --token-secondary-700: #334155;
  --token-secondary-800: #1e293b;
  /* Navy light */
  --token-secondary-900: #0f172a;
  /* Navy default */
  --token-secondary-950: #020617;
  /* Navy dark */

  /* Accent - Sky Blue */
  --token-accent-50: #f0f9ff;
  --token-accent-100: #e0f2fe;
  --token-accent-200: #bae6fd;
  --token-accent-300: #7dd3fc;
  --token-accent-400: #38bdf8;
  --token-accent-500: #0ea5e9;
  /* Main accent */
  --token-accent-600: #0284c7;
  --token-accent-700: #0369a1;
  --token-accent-800: #075985;
  --token-accent-900: #0c4a6e;
  --token-accent-950: #082f49;

  /* Success - Emerald Green */
  --token-success-50: #ecfdf5;
  --token-success-100: #d1fae5;
  --token-success-200: #a7f3d0;
  --token-success-300: #6ee7b7;
  --token-success-400: #34d399;
  --token-success-500: #10b981;
  --token-success-600: #059669;
  --token-success-700: #047857;
  --token-success-800: #065f46;
  --token-success-900: #064e3b;
  --token-success-950: #022c22;

  /* Warning - Amber */
  --token-warning-50: #fffbeb;
  --token-warning-100: #fef3c7;
  --token-warning-200: #fde68a;
  --token-warning-300: #fcd34d;
  --token-warning-400: #fbbf24;
  --token-warning-500: #f59e0b;
  --token-warning-600: #d97706;
  --token-warning-700: #b45309;
  --token-warning-800: #92400e;
  --token-warning-900: #78350f;
  --token-warning-950: #451a03;

  /* Error - Red */
  --token-error-50: #fef2f2;
  --token-error-100: #fee2e2;
  --token-error-200: #fecaca;
  --token-error-300: #fca5a5;
  --token-error-400: #f87171;
  --token-error-500: #ef4444;
  --token-error-600: #dc2626;
  --token-error-700: #b91c1c;
  --token-error-800: #991b1b;
  --token-error-900: #7f1d1d;
  --token-error-950: #450a0a;

  /* Neutral - Gray Scale */
  --token-neutral-50: #f9fafb;
  --token-neutral-100: #f3f4f6;
  --token-neutral-200: #e5e7eb;
  --token-neutral-300: #d1d5db;
  --token-neutral-400: #9ca3af;
  --token-neutral-500: #6b7280;
  --token-neutral-600: #4b5563;
  --token-neutral-700: #374151;
  --token-neutral-800: #1f2937;
  --token-neutral-900: #111827;
  --token-neutral-950: #030712;

  /* Surface - Canvas/Background */
  --token-surface-50: #ffffff;
  --token-surface-100: #fafafa;
  /* Light canvas */
  --token-surface-200: #f5f5f5;
  --token-surface-300: #eeeeee;
  --token-surface-400: #e0e0e0;
  --token-surface-500: #bdbdbd;
  --token-surface-600: #757575;
  --token-surface-700: #424242;
  --token-surface-800: #212121;
  --token-surface-900: #0f172a;
  /* Dark canvas */
  --token-surface-950: #000000;

  /* === SEMANTIC COLOR TOKENS === */
  /* Light Mode Semantic Colors */
  --token-bg-primary: var(--token-surface-50);
  /* #ffffff */
  --token-bg-secondary: var(--token-surface-100);
  /* #fafafa */
  --token-bg-tertiary: var(--token-surface-200);
  /* #f5f5f5 */

  --token-bg-frosted: rgba(15, 23, 42, 0.08);
  --token-bg-frosted-strong: rgba(255, 255, 255, 0.20);
  /* Enhanced frosted glass for better visibility */

  --token-hero-gradient-mid: #2f73e7;

  --token-text-primary: var(--token-secondary-900);
  /* #0f172a - WCAG AA: 15.8:1 */
  --token-text-secondary: #334155;
  /* WCAG AA compliant */
  --token-text-tertiary: var(--token-neutral-500);
  /* #6b7280 - WCAG AA: 4.6:1 */

  --token-border-primary: var(--token-neutral-200);
  /* #e5e7eb */
  --token-border-secondary: var(--token-neutral-300);
  /* #d1d5db */

  --token-shadow-primary: rgba(15, 23, 42, 0.08);
  --token-shadow-secondary: rgba(15, 23, 42, 0.12);

  /* Interactive States */
  --token-interactive-primary: var(--token-primary-500);
  /* #3b82f6 */
  --token-interactive-primary-hover: var(--token-primary-600);
  /* #2563eb */
  --token-interactive-primary-active: var(--token-primary-700);
  /* #1d4ed8 */

  --token-interactive-secondary: var(--token-neutral-100);
  /* #f3f4f6 */
  --token-interactive-secondary-hover: var(--token-neutral-200);
  /* #e5e7eb */
  --token-interactive-secondary-active: var(--token-neutral-300);
  /* #d1d5db */

  /* === RADIUS TOKENS === */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 9999px;

  /* === SPACING TOKENS (8px scale) === */
  --space-0: 0px;
  --space-1: 8px;
  --space-2: 16px;
  --space-3: 24px;
  --space-4: 32px;
  --space-5: 40px;
  --space-6: 48px;
  --space-8: 64px;
  --space-10: 80px;
  --space-12: 96px;
  --space-16: 128px;
  --space-20: 160px;
  --space-24: 192px;

  /* Component & Section Spacing */
  --space-component: var(--space-3);
  /* 24px */
  --space-section: var(--space-8);
  /* 64px */
}

/* === DARK MODE OVERRIDES === */
.dark {
  /* Dark Mode Semantic Colors */
  --token-bg-primary: var(--token-surface-900);
  /* #0f172a */
  --token-bg-secondary: var(--token-secondary-800);
  /* #1e293b */
  --token-bg-tertiary: var(--token-secondary-700);
  /* #334155 */

  --token-text-primary: #ffffff;
  /* Pure white for maximum contrast */
  --token-text-secondary: #E2E8F0;
  /* WCAG AA compliant */
  --token-text-tertiary: var(--token-neutral-400);
  /* #9ca3af - WCAG AA: 4.6:1 */

  --token-timeline-label: #60A5FA;

  --token-bg-frosted-strong: rgba(255, 255, 255, 0.12);
  /* Enhanced frosted glass for dark mode - lighter but not lighter than light mode */

  --token-border-primary: var(--token-secondary-700);
  /* #334155 */
  --token-border-secondary: var(--token-secondary-600);
  /* #475569 */

  --token-shadow-primary: rgba(0, 0, 0, 0.25);
  --token-shadow-secondary: rgba(0, 0, 0, 0.35);

  /* Interactive States - Dark Mode */
  --token-interactive-primary: var(--token-primary-400);
  /* #60a5fa */
  --token-interactive-primary-hover: var(--token-primary-300);
  /* #93c5fd */
  --token-interactive-primary-active: var(--token-primary-200);
  /* #bfdbfe */

  --token-interactive-secondary: var(--token-secondary-700);
  /* #334155 */
  --token-interactive-secondary-hover: var(--token-secondary-600);
  /* #475569 */
  --token-interactive-secondary-active: var(--token-secondary-500);
  /* #64748b */
}

/* === GRADIENT TOKENS === */
/* Preserves existing gradient color schemes for backgrounds and components */
:root {
  /* Primary gradients - Electric Blue theme */
  --gradient-primary: linear-gradient(135deg, var(--token-primary-500), var(--token-primary-600));
  /* #3b82f6 to #2563eb */
  --gradient-primary-light: linear-gradient(135deg, var(--token-primary-400), var(--token-primary-500));
  /* #60a5fa to #3b82f6 */

  /* Secondary gradients - Navy to Blue theme */
  --gradient-secondary: linear-gradient(135deg, var(--token-secondary-800), var(--token-primary-500));
  /* #1e293b to #3b82f6 */
  --gradient-secondary-dark: linear-gradient(135deg, var(--token-secondary-900), var(--token-primary-600));
  /* #0f172a to #2563eb */

  /* Header background gradient - Slate to Blue */
  --gradient-header: linear-gradient(to bottom right, var(--token-secondary-900), var(--token-hero-gradient-mid), var(--token-primary-600));
  /* Hero gradient middle stop for enhanced contrast */

  /* Button gradients - Blue theme */
  --gradient-button: linear-gradient(to right, var(--token-primary-500), var(--token-primary-600));
  /* blue-500 to blue-600 */
  --gradient-button-hover: linear-gradient(to right, var(--token-primary-600), var(--token-primary-700));
  /* blue-600 to blue-700 */

  /* Accent gradients */
  --gradient-accent: linear-gradient(135deg, var(--token-accent-500), var(--token-accent-600));

  /* Timeline gradients */
  --gradient-timeline: linear-gradient(to bottom, var(--token-primary-400), var(--token-primary-500), var(--token-primary-600));
}

.dark {
  /* Dark mode gradient overrides - preserves existing dark theme */
  --gradient-primary: linear-gradient(135deg, var(--token-primary-500), var(--token-primary-700));
  /* #3b82f6 to #1d4ed8 */
  --gradient-primary-light: linear-gradient(135deg, var(--token-primary-300), var(--token-primary-400));
  /* #93c5fd to #60a5fa */

  /* Secondary gradients for dark mode */
  --gradient-secondary: linear-gradient(135deg, var(--token-secondary-800), var(--token-primary-400));
  /* #1e293b to #60a5fa */
  --gradient-secondary-dark: linear-gradient(135deg, var(--token-secondary-900), var(--token-primary-500));
  /* #0f172a to #3b82f6 */

  /* Header background gradient - Enhanced for dark mode */
  --gradient-header: linear-gradient(to bottom right, var(--token-secondary-900), var(--token-primary-500));
  /* slate-900 to blue-500 */

  /* Button gradients - Adjusted for dark mode visibility */
  --gradient-button: linear-gradient(to right, var(--token-primary-400), var(--token-primary-500));
  /* blue-400 to blue-500 */
  --gradient-button-hover: linear-gradient(to right, var(--token-primary-300), var(--token-primary-400));
  /* blue-300 to blue-400 */

  /* Accent gradients for dark mode */
  --gradient-accent: linear-gradient(135deg, var(--token-accent-400), var(--token-accent-500));

  /* Timeline gradients for dark mode */
  --gradient-timeline: linear-gradient(to bottom, var(--token-primary-300), var(--token-primary-400), var(--token-primary-500));
}