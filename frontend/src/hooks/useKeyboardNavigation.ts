// ◀︎ LLM-modified
import { useEffect, useRef, useState } from 'react';

interface UseKeyboardNavigationOptions {
  isActive: boolean;
  itemCount: number;
  onSelect: (index: number) => void;
  onClose: () => void;
  loop?: boolean;
}

/**
 * Custom hook for keyboard navigation within a list of items
 * Handles arrow keys, Enter, and Escape
 * 
 * @param options Configuration options for keyboard navigation
 * @returns Current selected index and ref for the container
 */
export const useKeyboardNavigation = (options: UseKeyboardNavigationOptions) => {
  const { isActive, itemCount, onSelect, onClose, loop = true } = options;
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  const containerRef = useRef<HTMLDivElement>(null);

  // Reset selection when menu opens/closes or item count changes
  useEffect(() => {
    if (!isActive) {
      setSelectedIndex(-1);
    }
  }, [isActive, itemCount]);

  useEffect(() => {
    if (!isActive) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setSelectedIndex(prevIndex => {
            if (itemCount === 0) return -1;
            if (prevIndex === -1) return 0;
            if (prevIndex === itemCount - 1) {
              return loop ? 0 : prevIndex;
            }
            return prevIndex + 1;
          });
          break;

        case 'ArrowUp':
          event.preventDefault();
          setSelectedIndex(prevIndex => {
            if (itemCount === 0) return -1;
            if (prevIndex === -1) return itemCount - 1;
            if (prevIndex === 0) {
              return loop ? itemCount - 1 : prevIndex;
            }
            return prevIndex - 1;
          });
          break;

        case 'Enter':
          event.preventDefault();
          if (selectedIndex >= 0 && selectedIndex < itemCount) {
            onSelect(selectedIndex);
          }
          break;

        case 'Escape':
          event.preventDefault();
          onClose();
          break;

        case 'Home':
          event.preventDefault();
          if (itemCount > 0) {
            setSelectedIndex(0);
          }
          break;

        case 'End':
          event.preventDefault();
          if (itemCount > 0) {
            setSelectedIndex(itemCount - 1);
          }
          break;

        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isActive, itemCount, selectedIndex, onSelect, onClose, loop]);

  // Focus the selected item
  useEffect(() => {
    if (!isActive || selectedIndex === -1) return;

    const container = containerRef.current;
    if (!container) return;

    const items = container.querySelectorAll('[role="menuitem"]');
    const selectedItem = items[selectedIndex] as HTMLElement;

    if (selectedItem) {
      selectedItem.focus();
    }
  }, [selectedIndex, isActive]);

  return {
    selectedIndex,
    containerRef,
    setSelectedIndex
  };
};
