/* Import Space Grotesk font from Google Fonts - 2025's trending digital-friendly font */
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
:root {
  --primary: #3B82F6;
  --primary-dark: #2563EB;
  --primary-light: #93C5FD;
  --background: #FAFAFA;
  --background-dark: #0F172A;
  --text: #0F172A;
  --text-dark: #F8FAFC;
  --text-muted: #6B7280;
  --text-muted-dark: #9CA3AF;
  --border: #E5E7EB;
  --border-dark: #374151;
  --card: #FFFFFF;
  --card-dark: #1E293B;
  --accent: #0EA5E9;
  --gradient-from: #1E293B;
  --gradient-to: #3B82F6;
  --gradient-to-dark: #2563EB;
  --space-component: 24px;
  --space-section: 64px;
}

/* Smooth scrolling for the entire page, with reduced motion preference support */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-none-on-reduce-motion,
  .animate-gradient-shift,
  .animate-bounce-slow {
    animation: none !important;
    transition: none !important;
  }
}

body {
  @apply bg-canvas dark:bg-canvas-dark text-text dark:text-text-dark;
  font-family: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Font weight utilities */
.font-thin {
  font-weight: 100;
}
.font-extralight {
  font-weight: 200;
}
.font-light {
  font-weight: 300;
}
.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-black {
  font-weight: 900;
}

/* Focus styles for better accessibility */
*:focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900;
  transition: ring-color 0.2s ease-in-out;
}

/* Custom scrollbar styling for WebKit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800 rounded-full;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-700 rounded-full;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-600;
}

/* Command palette specific styles */
.cmdk-root {
  @apply w-full;
}

.cmdk-input {
  @apply w-full py-2 bg-transparent focus:outline-none text-gray-800 dark:text-gray-200;
}

.cmdk-list {
  @apply max-h-[300px] overflow-y-auto py-2;
}

.cmdk-group-heading {
  @apply px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider;
}

.cmdk-item {
  @apply px-3 py-2 rounded-md flex items-center justify-between cursor-pointer;
  transition: all 0.15s ease;
}

.cmdk-item[data-selected="true"] {
  @apply bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300;
}

.cmdk-item:not([data-selected="true"]) {
  @apply text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800;
}

.cmdk-empty {
  @apply py-6 text-center text-gray-500 dark:text-gray-400;
}

/* Timeline card interaction styles */
.timeline-element-container {
  position: relative;
  transition: z-index 0s 0.3s;
}

.timeline-element-container.z-10 {
  transition: z-index 0s 0s;
}

.timeline-card {
  transition: opacity 0.3s ease;
}

.timeline-card:focus {
  @apply outline-none;
}

.timeline-card:focus-visible {
  @apply ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900;
}

.vertical-timeline-element-content {
  transition: background-color 0.3s ease, border-left 0.3s ease, box-shadow 0.3s ease;
}

.vertical-timeline-element-content-arrow {
  transition: border-right 0.3s ease;
}

/* Skills component styles */
.skill-item {
  transition: transform 0.3s ease, opacity 0.3s ease, background-color 0.3s ease;
}

.skill-item:hover {
  transform: translateY(-2px);
}

.skill-item:focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900;
}

.skill-category-button {
  transition: background-color 0.3s ease, color 0.3s ease, transform 0.2s ease;
}

.skill-category-button:hover {
  transform: translateY(-1px);
}

.skill-category-button:focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-900;
}

.skill-category-button.active {
  @apply bg-blue-600 text-white shadow-md;
}

/* Keyboard navigation visual indicators */
kbd {
  @apply px-1.5 py-0.5 text-xs rounded bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

/* Animation utilities */
.transition-all-200 {
  transition: all 0.2s ease;
}

.transition-all-300 {
  transition: all 0.3s ease;
}

.transition-transform-200 {
  transition: transform 0.2s ease;
}

.transition-opacity-300 {
  transition: opacity 0.3s ease;
}

.transition-colors-200 {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Helper classes for focus states */
.focus-ring {
  @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 dark:focus-visible:ring-offset-gray-900;
}

.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  @apply shadow-md;
}

/* Scrollbar for command palette */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-300 {
  scrollbar-color: #D1D5DB transparent;
}

.dark .scrollbar-thumb-gray-700 {
  scrollbar-color: #374151 transparent;
}

/* Subtle animations for first-time visitors */
@keyframes subtle-pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* New animation keyframes */
@keyframes sheen {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes bounce-slow {
  0%, 100% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-subtle-pulse {
  animation: subtle-pulse 2s ease-in-out infinite;
}

.animate-gradient-shift {
  animation: sheen 2.5s ease-in-out infinite;
}

.animate-bounce-slow {
  animation: bounce-slow 3s infinite;
}

/* Ensure proper stacking for interactive elements */
.z-command-palette {
  z-index: 50;
}

.z-timeline-expanded {
  z-index: 10;
}

/* Custom utility for the timeline left border highlight */
.border-l-highlight {
  @apply border-l-4 border-blue-500;
  transition: border-left-color 0.3s ease;
}

/* Organic flowing layout helpers for Skills component */
.organic-position {
  position: absolute;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.skill-filter-active {
  @apply scale-105 shadow-md;
}

.skill-filter-inactive {
  @apply scale-90 opacity-40;
}

/* Icon button utilities */
.icon-btn-primary {
  background: linear-gradient(90deg, var(--gradient-from), var(--gradient-to), var(--gradient-to-dark));
  background-size: 200% 100%;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.icon-btn-primary:hover {
  animation: sheen 2.5s ease-in-out infinite;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Ensure proper text contrast in all states */
.text-high-contrast {
  @apply text-gray-900 dark:text-white;
}

.text-medium-contrast {
  @apply text-gray-700 dark:text-gray-300;
}

.text-low-contrast {
  @apply text-gray-500 dark:text-gray-400;
}
