#!/bin/bash

# <PERSON>er <PERSON>folio - Unified Development Environment Startup Script
# Supports starting frontend, backend, or both with proper process management

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [frontend|backend|both|stop|status]"
    echo ""
    echo "Commands:"
    echo "  frontend  - Start only the frontend development server (port 3000)"
    echo "  backend   - Start only the backend server (port 8000)"
    echo "  both      - Start both frontend and backend servers"
    echo "  stop      - Stop all development servers"
    echo "  status    - Show status of development servers"
    echo "  help      - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 frontend    # Start only React dev server"
    echo "  $0 backend     # Start only FastAPI server"
    echo "  $0 both        # Start both servers"
    echo "  $0 stop        # Stop all servers"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is in use
port_in_use() {
    local port=$1
    if command_exists lsof; then
        lsof -ti:$port >/dev/null 2>&1
    elif command_exists netstat; then
        netstat -tulpn 2>/dev/null | grep ":$port" >/dev/null
    else
        return 1
    fi
}

# Function to get process info for port
get_port_process() {
    local port=$1
    if command_exists lsof; then
        lsof -ti:$port 2>/dev/null
    elif command_exists netstat; then
        netstat -tulpn 2>/dev/null | grep ":$port" | awk '{print $7}' | cut -d'/' -f1
    fi
}

# Function to kill processes on port
cleanup_port() {
    local port=$1
    local service_name=$2
    
    if port_in_use $port; then
        local pid=$(get_port_process $port)
        if [ ! -z "$pid" ]; then
            log_warning "Stopping $service_name (PID: $pid) on port $port"
            kill -TERM $pid 2>/dev/null || kill -9 $pid 2>/dev/null || true
            sleep 2
            
            # Check if process is still running
            if port_in_use $port; then
                log_warning "Force killing $service_name on port $port"
                kill -9 $pid 2>/dev/null || true
                sleep 1
            fi
        fi
    fi
}

# Function to show server status
show_status() {
    log_header "Development Server Status"
    
    echo "Frontend (port 3000):"
    if port_in_use 3000; then
        local pid=$(get_port_process 3000)
        echo -e "  ${GREEN}✅ Running${NC} (PID: $pid)"
        echo "  URL: http://localhost:3000"
    else
        echo -e "  ${RED}❌ Not running${NC}"
    fi
    
    echo ""
    echo "Backend (port 8000):"
    if port_in_use 8000; then
        local pid=$(get_port_process 8000)
        echo -e "  ${GREEN}✅ Running${NC} (PID: $pid)"
        echo "  URL: http://localhost:8000"
    else
        echo -e "  ${RED}❌ Not running${NC}"
    fi
}

# Function to stop all servers
stop_servers() {
    log_header "Stopping Development Servers"
    
    cleanup_port 3000 "Frontend"
    cleanup_port 8000 "Backend"
    cleanup_port 8001 "Backend (alt)"
    
    log_success "All development servers stopped"
}

# Function to start frontend
start_frontend() {
    log_header "Starting Frontend Development Server"

    # Get project root from script location
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

    # Check if frontend directory exists
    if [ ! -d "$PROJECT_ROOT/frontend" ]; then
        log_error "Frontend directory not found"
        exit 1
    fi

    # Navigate to frontend directory and start with yarn (what actually works)
    cd "$PROJECT_ROOT/frontend"

    # Check if yarn is available
    if ! command -v yarn >/dev/null 2>&1; then
        log_error "Yarn is not installed"
        exit 1
    fi

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log_info "Installing dependencies..."
        yarn install
    fi

    log_success "Starting development server at http://localhost:3000"
    yarn start
}

# Function to start backend
start_backend() {
    log_header "Starting Backend Development Server"

    # Get script directory
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    # Check if start-backend.sh exists
    if [ ! -f "$SCRIPT_DIR/start-backend.sh" ]; then
        log_error "start-backend.sh not found in scripts directory"
        exit 1
    fi

    # Make sure it's executable
    chmod +x "$SCRIPT_DIR/start-backend.sh"

    # Start backend
    "$SCRIPT_DIR/start-backend.sh"
}

# Function to start both servers
start_both() {
    log_header "Starting Both Frontend and Backend Servers"

    # Get project root from script location
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

    # Check if directories exist
    if [ ! -d "$PROJECT_ROOT/frontend" ] || [ ! -d "$PROJECT_ROOT/backend" ]; then
        log_error "Frontend or backend directory not found"
        exit 1
    fi

    # Start backend in background
    log_info "Starting backend server in background..."
    if [ -f "$SCRIPT_DIR/start-backend.sh" ]; then
        chmod +x "$SCRIPT_DIR/start-backend.sh"
        "$SCRIPT_DIR/start-backend.sh" &
        BACKEND_PID=$!
    else
        log_warning "Backend script not found, skipping backend"
    fi

    # Wait a moment for backend to start
    sleep 2

    # Start frontend (what actually works)
    log_info "Starting frontend server..."
    cd "$PROJECT_ROOT/frontend"

    if [ ! -d "node_modules" ]; then
        log_info "Installing frontend dependencies..."
        yarn install
    fi

    log_success "Starting development server at http://localhost:3000"
    yarn start
}

# Main function
main() {
    local command=${1:-help}
    
    case $command in
        "frontend"|"front"|"f")
            start_frontend
            ;;
        "backend"|"back"|"b")
            start_backend
            ;;
        "both"|"all"|"full")
            start_both
            ;;
        "stop"|"kill")
            stop_servers
            ;;
        "status"|"ps")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "Unknown command: $command"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
