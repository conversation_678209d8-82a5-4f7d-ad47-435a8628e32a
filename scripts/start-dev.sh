#!/bin/bash

# <PERSON> Portfolio - Frontend Development Server
# Simple script that does what actually works: cd frontend && yarn start

echo "🚀 Starting Nathan Dryer Portfolio Frontend..."

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"

# Check if frontend directory exists
if [ ! -d "$FRONTEND_DIR" ]; then
    echo "❌ Frontend directory not found: $FRONTEND_DIR"
    exit 1
fi

# Navigate to frontend directory (this is the key step that works)
cd "$FRONTEND_DIR"

# Check if yarn is available
if ! command -v yarn >/dev/null 2>&1; then
    echo "❌ Yarn is not installed. Please install yarn first."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    yarn install
fi

echo "✅ Starting development server at http://localhost:3000"
echo "📁 Working directory: $(pwd)"

# This is what actually works - simple yarn start from frontend directory
yarn start
