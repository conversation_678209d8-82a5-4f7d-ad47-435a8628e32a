#!/bin/bash

# <PERSON> - Backend Server Startup Script
# Optimized for local development with proper error handling

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to kill processes on port
cleanup_port() {
    local port=$1
    log_info "Checking for processes on port $port..."
    
    if command_exists lsof; then
        local pid=$(lsof -ti:$port 2>/dev/null)
        if [ ! -z "$pid" ]; then
            log_warning "Killing process $pid on port $port"
            kill -9 $pid 2>/dev/null || true
            sleep 1
        fi
    elif command_exists netstat; then
        local pid=$(netstat -tulpn 2>/dev/null | grep ":$port" | awk '{print $7}' | cut -d'/' -f1)
        if [ ! -z "$pid" ]; then
            log_warning "Killing process $pid on port $port"
            kill -9 $pid 2>/dev/null || true
            sleep 1
        fi
    fi
}

# Function to validate Python environment
validate_python_env() {
    if ! command_exists python3; then
        log_error "Python 3 is not installed or not in PATH"
        exit 1
    fi
    
    local python_version=$(python3 --version | cut -d' ' -f2)
    local major_version=$(echo $python_version | cut -d. -f1)
    local minor_version=$(echo $python_version | cut -d. -f2)
    
    if [ "$major_version" -lt 3 ] || ([ "$major_version" -eq 3 ] && [ "$minor_version" -lt 8 ]); then
        log_error "Python version $python_version is too old. Please upgrade to Python 3.8 or higher."
        exit 1
    fi
    
    log_success "Python $python_version detected"
}

# Function to setup virtual environment
setup_venv() {
    local backend_dir=$1
    local venv_dir="$backend_dir/venv"
    
    if [ ! -d "$venv_dir" ]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv "$venv_dir"
        log_success "Virtual environment created"
    fi
    
    # Activate virtual environment
    source "$venv_dir/bin/activate"
    log_success "Virtual environment activated"
    
    # Upgrade pip
    pip install --upgrade pip > /dev/null 2>&1
    
    # Install dependencies
    if [ -f "$backend_dir/requirements.txt" ]; then
        log_info "Installing Python dependencies..."
        pip install -r "$backend_dir/requirements.txt"
        log_success "Dependencies installed successfully"
    else
        log_warning "No requirements.txt found, skipping dependency installation"
    fi
}

# Function to check environment variables
check_env_vars() {
    local backend_dir=$1
    local env_file="$backend_dir/.env"
    
    if [ ! -f "$env_file" ]; then
        log_warning "No .env file found in backend directory"
        log_info "Creating .env file from .env.example if available..."
        
        if [ -f "$backend_dir/.env.example" ]; then
            cp "$backend_dir/.env.example" "$env_file"
            log_warning "Please edit $env_file with your configuration"
        elif [ -f "$(dirname "$backend_dir")/.env.example" ]; then
            cp "$(dirname "$backend_dir")/.env.example" "$env_file"
            log_warning "Please edit $env_file with your configuration"
        else
            log_warning "No .env.example found. You may need to create $env_file manually"
        fi
    else
        log_success "Environment file found: $env_file"
    fi
}

# Main startup function
main() {
    log_info "🚀 Starting Nathan Dryer Portfolio Backend Server..."
    
    # Get the script directory and navigate to project root
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
    BACKEND_DIR="$PROJECT_ROOT/backend"
    
    log_info "📁 Project root: $PROJECT_ROOT"
    log_info "📁 Backend directory: $BACKEND_DIR"
    
    # Validate backend directory exists
    if [ ! -d "$BACKEND_DIR" ]; then
        log_error "Backend directory not found: $BACKEND_DIR"
        exit 1
    fi
    
    # Change to backend directory
    cd "$BACKEND_DIR"
    
    # Validate Python environment
    validate_python_env
    
    # Clean up any existing processes on port 8000
    cleanup_port 8000
    cleanup_port 8001
    
    # Setup virtual environment and dependencies
    setup_venv "$BACKEND_DIR"
    
    # Check environment variables
    check_env_vars "$BACKEND_DIR"
    
    # Display environment information
    log_info "🔧 Environment Information:"
    echo "   Python version: $(python --version)"
    echo "   Working directory: $(pwd)"
    echo "   Virtual environment: activated"
    
    # Check if server.py exists
    if [ ! -f "server.py" ]; then
        log_error "server.py not found in $BACKEND_DIR"
        exit 1
    fi
    
    # Start the backend server
    log_info "🌐 Starting FastAPI backend server on http://localhost:8000"
    log_info "Press Ctrl+C to stop the server"
    
    # Use uvicorn to start the FastAPI server
    if command_exists uvicorn; then
        uvicorn server:app --host 0.0.0.0 --port 8000 --reload
    else
        log_error "uvicorn not found. Installing uvicorn..."
        pip install uvicorn
        uvicorn server:app --host 0.0.0.0 --port 8000 --reload
    fi
}

# Error handling
trap 'log_error "Script interrupted"; exit 1' INT TERM

# Run main function
main "$@"
